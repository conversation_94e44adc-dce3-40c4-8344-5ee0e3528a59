import {BottomSheetModal} from '@gorhom/bottom-sheet';
import React, {forwardRef, memo, useCallback, useMemo, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {showMessage} from 'react-native-flash-message';

import MButton from '@/components/MButton';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {
  useForgotPasswordMutation,
  validateEmailWithMessage,
} from '@/hooks/useForgotPassword';
import {BodyM} from '@/styles/styled-components';
import theme from '@/styles/theme';
import BaseModal from './BaseModal';
import {showSuccessToast} from '@/utils/toast';

interface ForgotPasswordModalProps {
  onClose?: () => void;
}

const ForgotPasswordModal = forwardRef<BottomSheetModal, ForgotPasswordModalProps>(
  ({onClose}, ref) => {
    const [email, setEmail] = useState('');
    const [emailError, setEmailError] = useState<string | undefined>();

    const forgotPasswordMutation = useForgotPasswordMutation();

    // Validate email in real-time
    const emailValidation = useMemo(() => {
      if (!email) return {isValid: false, message: undefined};
      return validateEmailWithMessage(email);
    }, [email]);

    // // Handle email input change
    // const handleEmailChange = useCallback(
    //   (text: string) => {
    //     setEmail(text);
    //     // Clear error when user starts typing
    //     if (emailError) {
    //       setEmailError(undefined);
    //     }
    //   },
    //   [emailError],
    // );

    // Handle form submission
    const handleSubmit = useCallback(async () => {
      // Validate email before submission
      const validation = validateEmailWithMessage(email);
      if (!validation.isValid) {
        setEmailError(validation.message);
        return;
      }

      // Clear any existing errors
      setEmailError(undefined);

      // Submit forgot password request
      //   forgotPasswordMutation.mutate(
      //     {email},
      //     {
      //       onSuccess: () => {
      //         // Show success toast matching the design
      //         showSuccessToast(
      //           `Check your inbox!\nA link to reset your password has been sent to ${email.trim()}`,
      //         );

      //         // Close modal
      //         if (ref && 'current' in ref) {
      //           ref.current?.dismiss();
      //         }
      //         onClose?.();
      //       },
      //       onError: (error) => {
      //         // Show error toast
      //         showWarningToast(
      //           error.message || 'Failed to send reset link. Please try again.',
      //         );
      //       },
      //     },
      //   );
      showSuccessToast(
        `Check your inbox! A link to reset your password has been sent to your email: ${email.trim()}`,
      );
    }, [email, ref, onClose]);

    // Check if form is valid for button state
    const isFormValid = useMemo(() => {
      return emailValidation.isValid && !forgotPasswordMutation.isPending;
    }, [emailValidation.isValid, forgotPasswordMutation.isPending]);

    return (
      <BaseModal ref={ref} snapPoints={['90%']} title="Reset Password">
        <View style={styles.container}>
          <BodyM style={styles.description}>
            Enter the email address associated with your account, and we'll email you a
            link to reset your password
          </BodyM>

          <View style={styles.inputContainer}>
            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email address"
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect={false}
              //   error={emailError}
              //   styles={{
              // inputContainer: styles.emailInput,
              //   }}
            />
          </View>

          <View style={styles.buttonContainer}>
            <MButton
              text="Send reset link"
              onPress={handleSubmit}
              disabled={!isFormValid}
              isLoading={forgotPasswordMutation.isPending}
              loadingText="Sending..."
              variant="primary"
            />
          </View>
        </View>
      </BaseModal>
    );
  },
);

export default memo(ForgotPasswordModal);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.md,
  },
  description: {
    color: GlobalStyles.gray.gray900,
    lineHeight: 22,
    marginBottom: theme.spacing.xl,
    textAlign: 'left',
  },
  inputContainer: {
    marginBottom: theme.spacing.xl,
  },
  emailInput: {
    backgroundColor: GlobalStyles.base.white,
    borderColor: GlobalStyles.gray.gray300,
    borderRadius: 8,
  },
  buttonContainer: {
    marginTop: theme.spacing.md,
  },
});
