import {BottomSheetModal} from '@gorhom/bottom-sheet';
import React, {forwardRef, memo} from 'react';
import {View} from 'react-native';

import BaseModal from './BaseModal';

const ForgotPasswordModal = forwardRef<BottomSheetModal>((ref) => {
  return (
    <BaseModal ref={ref} snapPoints={['80%']} title="Forgot Password">
      <View></View>
    </BaseModal>
  );
});

export default memo(ForgotPasswordModal);
